# TEE_CheckMemoryAccessRights 最终统一实现方案

## 1. 方案概述

基于对 OP-TEE 真实源码的分析，采用简化的三步检查策略：
1. **MMU权限检查**：通过系统调用检查页面权限
2. **TA参数重叠检查**：防止访问其他TA的参数
3. **堆内存安全检查**：保护堆内存完整性

## 2. 核心系统调用设计

### 2.1 系统调用定义

```c
// 系统调用号：0x42
DEF_SYSCALL(0x42, _rctee_check_memory_access_rights, long, 3, void *buffer, uint32_t size, uint32_t access_flags)
```

### 2.2 内核实现

```c
// kernel/rctee/lib/rctee/rctee_core/syscall.c
long sys__rctee_check_memory_access_rights(user_addr_t buffer, uint32_t size, uint32_t access_flags) {
    struct rctee_app *rctee_app = current_rctee_app();
    vaddr_t vaddr = (vaddr_t)buffer;
    vaddr_t end_addr = vaddr + size;

    /* 基本参数验证 */
    if (!buffer || !size) return ERR_INVALID_ARGS;
    if (end_addr < vaddr) return ERR_INVALID_ARGS;  // 溢出检查

    /* 核心功能：逐页MMU权限检查 */
    while (vaddr < end_addr) {
        uint32_t actual_flags;
        paddr_t paddr;

        status_t ret = arch_mmu_query(&rctee_app->aspace->arch_aspace, vaddr, &paddr, &actual_flags);
        if (ret != NO_ERROR) return ERR_ACCESS_DENIED;

        /* 检查基本用户权限 - 所有用户空间内存都必须有USER权限 */
        if (!(actual_flags & ARCH_MMU_FLAG_PERM_USER)) return ERR_ACCESS_DENIED;

        /* 检查读权限 - TEE_MEMORY_ACCESS_READ 要求有用户权限 */
        if ((access_flags & TEE_MEMORY_ACCESS_READ)) {
            /* 已经在上面检查过 USER 权限，有 USER 权限就可读 */
        }

        /* 检查写权限 - TEE_MEMORY_ACCESS_WRITE 要求非只读 */
        if ((access_flags & TEE_MEMORY_ACCESS_WRITE) &&
            (actual_flags & ARCH_MMU_FLAG_PERM_RO)) return ERR_ACCESS_DENIED;

        vaddr = ROUNDUP(vaddr + 1, PAGE_SIZE);
    }

    return NO_ERROR;
}
```

## 3. 标志转换与比较逻辑

### 3.1 标志格式对比

**用户传入的 TEE 标准标志**：
```c
#define TEE_MEMORY_ACCESS_READ          0x00000001  // 读权限
#define TEE_MEMORY_ACCESS_WRITE         0x00000002  // 写权限
#define TEE_MEMORY_ACCESS_ANY_OWNER     0x00000004  // 任意所有者
```

**arch_mmu_query 返回的 Trusty MMU 标志**：
```c
#define ARCH_MMU_FLAG_PERM_USER         0x00000004  // 用户可访问
#define ARCH_MMU_FLAG_PERM_RO           0x00000008  // 只读权限
#define ARCH_MMU_FLAG_PERM_NO_EXECUTE   0x00000010  // 禁止执行
#define ARCH_MMU_FLAG_NS                0x00000020  // 非安全
```

### 3.2 正确的权限检查逻辑

**关键理解**：
1. **不能直接比较两种格式的标志**
2. **需要理解每种标志的语义含义**
3. **按照 OP-TEE 的逻辑进行权限验证**

**正确的检查逻辑**：
```c
/* 检查基本用户权限 */
if (!(actual_flags & ARCH_MMU_FLAG_PERM_USER))
    return ERR_ACCESS_DENIED;

/* 检查读权限 - TEE_MEMORY_ACCESS_READ */
if ((access_flags & TEE_MEMORY_ACCESS_READ)) {
    /* 在 Trusty 中，有 ARCH_MMU_FLAG_PERM_USER 就意味着可读 */
    /* 已经在上面检查过了，不需要额外检查 */
}

/* 检查写权限 - TEE_MEMORY_ACCESS_WRITE */
if ((access_flags & TEE_MEMORY_ACCESS_WRITE)) {
    /* 要求：有用户权限 + 非只读 */
    if (actual_flags & ARCH_MMU_FLAG_PERM_RO)
        return ERR_ACCESS_DENIED;
}

/* 检查所有者权限 - TEE_MEMORY_ACCESS_ANY_OWNER */
if (!(access_flags & TEE_MEMORY_ACCESS_ANY_OWNER)) {
    /* 如果没有设置 ANY_OWNER，则必须是 TA 私有内存 */
    /* 这个检查需要额外的内存类型判断逻辑 */
}
```

### 3.3 OP-TEE 的做法分析

根据 OP-TEE 源码分析，`_utee_check_access_rights` 系统调用：

1. **接收 TEE 标准标志**：用户传入 `TEE_MEMORY_ACCESS_*` 标志
2. **内核内部转换**：将 TEE 标志转换为架构特定的 MMU 标志
3. **MMU 权限查询**：使用转换后的标志查询页面权限
4. **权限比较验证**：比较期望权限与实际权限

**OP-TEE 的权限映射**：
- `TEE_MEMORY_ACCESS_READ` → 检查页面是否用户可读
- `TEE_MEMORY_ACCESS_WRITE` → 检查页面是否用户可写
- `TEE_MEMORY_ACCESS_ANY_OWNER` → 检查是否允许访问共享内存

## 4. 改进的系统调用实现（正确处理标志转换）

### 4.1 修正后的系统调用

```c
// kernel/rctee/lib/rctee/rctee_core/syscall.c
long sys__rctee_check_memory_access_rights(user_addr_t buffer, uint32_t size, uint32_t access_flags) {
    struct rctee_app *rctee_app = current_rctee_app();
    vaddr_t vaddr = (vaddr_t)buffer;
    vaddr_t end_addr = vaddr + size;

    /* 基本参数验证 */
    if (!buffer || !size) return ERR_INVALID_ARGS;
    if (end_addr < vaddr) return ERR_INVALID_ARGS;  // 溢出检查

    /* 验证 TEE 标志的有效性 */
    uint32_t valid_flags = TEE_MEMORY_ACCESS_READ | TEE_MEMORY_ACCESS_WRITE | TEE_MEMORY_ACCESS_ANY_OWNER;
    if (access_flags & ~valid_flags) return ERR_INVALID_ARGS;

    /* 核心功能：逐页MMU权限检查 */
    while (vaddr < end_addr) {
        uint32_t actual_mmu_flags;
        paddr_t paddr;

        status_t ret = arch_mmu_query(&rctee_app->aspace->arch_aspace, vaddr, &paddr, &actual_mmu_flags);
        if (ret != NO_ERROR) return ERR_ACCESS_DENIED;

        /* 所有用户空间内存都必须有 USER 权限 */
        if (!(actual_mmu_flags & ARCH_MMU_FLAG_PERM_USER))
            return ERR_ACCESS_DENIED;

        /* 检查读权限 - TEE_MEMORY_ACCESS_READ */
        if (access_flags & TEE_MEMORY_ACCESS_READ) {
            /* 在 Trusty 中，有 USER 权限就可读，已经检查过了 */
        }

        /* 检查写权限 - TEE_MEMORY_ACCESS_WRITE */
        if (access_flags & TEE_MEMORY_ACCESS_WRITE) {
            if (actual_mmu_flags & ARCH_MMU_FLAG_PERM_RO)
                return ERR_ACCESS_DENIED;
        }

        /* 检查所有者权限 - TEE_MEMORY_ACCESS_ANY_OWNER */
        if (!(access_flags & TEE_MEMORY_ACCESS_ANY_OWNER)) {
            /* 如果没有设置 ANY_OWNER，检查是否为 TA 私有内存 */
            if (!is_ta_private_memory(rctee_app, vaddr))
                return ERR_ACCESS_DENIED;
        }

        vaddr = ROUNDUP(vaddr + 1, PAGE_SIZE);
    }

    return NO_ERROR;
}

/**
 * 检查是否为 TA 私有内存（简化版本）
 */
/**
 * 检查内存是否为 TA 私有内存
 * 基于 Trusty 的内存管理机制进行精确判断
 */
static bool is_ta_private_memory(struct rctee_app *rctee_app, vaddr_t vaddr, size_t size) {
    vaddr_t end_addr = vaddr + size;

    /* 1. 检查是否为共享内存 (FF-A) */
    if (is_shared_memory_region(rctee_app, vaddr, size)) {
        return false;  // 共享内存不是私有的
    }

    /* 2. 检查是否在 TA 的地址空间范围内 */
    if (!is_in_ta_address_space(rctee_app, vaddr, size)) {
        return false;  // 超出 TA 地址空间
    }

    /* 3. 检查具体内存区域类型 */
    while (vaddr < end_addr) {
        enum trusty_memory_type mem_type = get_memory_type(rctee_app, vaddr);

        switch (mem_type) {
            case TRUSTY_MEM_PRIVATE:
            case TRUSTY_MEM_HEAP:
            case TRUSTY_MEM_STACK:
            case TRUSTY_MEM_CODE:
            case TRUSTY_MEM_DATA:
                /* 这些都是 TA 私有内存 */
                break;

            case TRUSTY_MEM_SHARED:
                /* 共享内存不是私有的 */
                return false;

            default:
                /* 未知类型，保守处理 */
                return false;
        }

        vaddr = ROUNDUP(vaddr + 1, PAGE_SIZE);
    }

    return true;  // 所有页面都是私有的
}

/**
 * 检查是否为共享内存区域
 */
static bool is_shared_memory_region(struct rctee_app *rctee_app, vaddr_t vaddr, size_t size) {
    struct vmm_aspace *aspace = rctee_app->aspace;
    vaddr_t end_addr = vaddr + size;

    /* 遍历地址空间中的所有区域 */
    struct vmm_region *region;
    list_for_every_entry(&aspace->region_list, region, struct vmm_region, node) {
        /* 检查是否与指定范围重叠 */
        if (vaddr < (region->base + region->size) && end_addr > region->base) {
            /* 检查区域标志，判断是否为共享内存 */
            if (region->flags & VMM_FLAG_SHARED) {
                return true;  // 发现共享内存区域
            }

            /* 检查是否为 FF-A 共享内存对象 */
            if (region->obj && is_ffa_shared_memory_obj(region->obj)) {
                return true;  // FF-A 共享内存
            }
        }
    }

    return false;  // 没有发现共享内存
}

/**
 * 检查是否在 TA 地址空间范围内
 */
static bool is_in_ta_address_space(struct rctee_app *rctee_app, vaddr_t vaddr, size_t size) {
    struct vmm_aspace *aspace = rctee_app->aspace;
    vaddr_t end_addr = vaddr + size;

    /* 检查地址范围是否在用户空间 */
    if (vaddr < aspace->base || end_addr > (aspace->base + aspace->size)) {
        return false;  // 超出地址空间范围
    }

    return true;
}

/**
 * 获取内存类型
 */
static enum trusty_memory_type get_memory_type(struct rctee_app *rctee_app, vaddr_t vaddr) {
    struct vmm_aspace *aspace = rctee_app->aspace;
    struct vmm_region *region;

    /* 查找包含该地址的区域 */
    list_for_every_entry(&aspace->region_list, region, struct vmm_region, node) {
        if (vaddr >= region->base && vaddr < (region->base + region->size)) {
            /* 根据区域属性判断内存类型 */
            if (region->flags & VMM_FLAG_SHARED) {
                return TRUSTY_MEM_SHARED;
            }

            /* 根据权限和用途判断 */
            uint32_t flags = region->arch_mmu_flags;

            if (flags & ARCH_MMU_FLAG_PERM_EXECUTE) {
                return TRUSTY_MEM_CODE;  // 可执行 = 代码段
            } else if (flags & ARCH_MMU_FLAG_PERM_WRITE) {
                /* 可写内存，进一步判断是堆还是数据段 */
                if (is_heap_region(rctee_app, vaddr)) {
                    return TRUSTY_MEM_HEAP;
                } else if (is_stack_region(rctee_app, vaddr)) {
                    return TRUSTY_MEM_STACK;
                } else {
                    return TRUSTY_MEM_DATA;  // 数据段
                }
            } else {
                return TRUSTY_MEM_DATA;  // 只读数据
            }
        }
    }

    return TRUSTY_MEM_PRIVATE;  // 默认为私有内存
}

/**
 * 检查是否为 FF-A 共享内存对象
 */
static bool is_ffa_shared_memory_obj(struct vmm_obj *obj) {
    /* 检查对象类型是否为共享内存 */
    if (obj->ops && obj->ops->get_page) {
        /* 通过对象操作函数判断是否为共享内存 */
        return (obj->ops == &shared_mem_obj_ops);
    }

    return false;
}

/**
 * 检查是否为堆区域
 */
static bool is_heap_region(struct rctee_app *rctee_app, vaddr_t vaddr) {
    /* 检查是否在堆地址范围内 */
    if (rctee_app->heap_base && rctee_app->heap_size) {
        return (vaddr >= rctee_app->heap_base &&
                vaddr < (rctee_app->heap_base + rctee_app->heap_size));
    }

    return false;
}

/**
 * 检查是否为栈区域
 */
static bool is_stack_region(struct rctee_app *rctee_app, vaddr_t vaddr) {
    /* 检查是否在栈地址范围内 */
    if (rctee_app->stack_base && rctee_app->stack_size) {
        return (vaddr >= rctee_app->stack_base &&
                vaddr < (rctee_app->stack_base + rctee_app->stack_size));
    }

    return false;
}
```

### 4.2 关键修正点

1. **正确的标志验证**：验证传入的 TEE 标志有效性
2. **清晰的变量命名**：`actual_mmu_flags` 明确表示这是 MMU 标志
3. **正确的权限映射**：
   - `TEE_MEMORY_ACCESS_READ` → 检查 `ARCH_MMU_FLAG_PERM_USER`
   - `TEE_MEMORY_ACCESS_WRITE` → 检查 `!(ARCH_MMU_FLAG_PERM_RO)`
   - `TEE_MEMORY_ACCESS_ANY_OWNER` → 检查内存所有权

## 5. 用户空间实现

### 3.1 主函数实现

```c
// user/base/lib/libutee/tee_api.c
TEE_Result TEE_CheckMemoryAccessRights(uint32_t accessFlags, void *buffer, size_t size)
{
    uint32_t flags = accessFlags;

    /* GP标准：size为0时直接返回成功 */
    if (!size) return TEE_SUCCESS;

    /* GP标准：NULL指针检查 */
    if (!buffer) return TEE_ERROR_ACCESS_DENIED;

    /* GP标准：地址溢出检查 */
    if ((uintptr_t)buffer + size < (uintptr_t)buffer)
        return TEE_ERROR_ACCESS_DENIED;

    /* GP标准：标志位验证 */
    if (accessFlags & ~(TEE_MEMORY_ACCESS_READ | TEE_MEMORY_ACCESS_WRITE |
                       TEE_MEMORY_ACCESS_ANY_OWNER))
        return TEE_ERROR_BAD_PARAMETERS;

    /* 第一步：MMU权限检查 */
    if (_rctee_check_memory_access_rights(buffer, size, accessFlags))
        return TEE_ERROR_ACCESS_DENIED;

    /* 第二步：TA参数兼容性检查 */
    flags &= TEE_MEMORY_ACCESS_READ | TEE_MEMORY_ACCESS_WRITE | TEE_MEMORY_ACCESS_ANY_OWNER;
    if (check_mem_access_rights_params(flags, buffer, size))
        return TEE_ERROR_ACCESS_DENIED;

    /* 第三步：堆内存重叠检查 */
    if (trusty_buffer_overlaps_heap(buffer, size) &&
        !trusty_buffer_is_within_allocated(buffer, size))
        return TEE_ERROR_ACCESS_DENIED;

    return TEE_SUCCESS;
}
```

### 3.2 辅助函数实现

```c
/**
 * 检查与TA参数的重叠
 */
static TEE_Result check_mem_access_rights_params(uint32_t flags, void *buf, size_t len)
{
    size_t n = 0;

    for (n = 0; n < TEE_NUM_PARAMS; n++) {
        uint32_t f = TEE_MEMORY_ACCESS_ANY_OWNER;

        switch (TEE_PARAM_TYPE_GET(ta_param_types, n)) {
        case TEE_PARAM_TYPE_MEMREF_OUTPUT:
        case TEE_PARAM_TYPE_MEMREF_INOUT:
            f |= TEE_MEMORY_ACCESS_WRITE;
            fallthrough;
        case TEE_PARAM_TYPE_MEMREF_INPUT:
            f |= TEE_MEMORY_ACCESS_READ;
            if (bufs_intersect(buf, len,
                              ta_params[n].memref.buffer,
                              ta_params[n].memref.size)) {
                if (!(flags & TEE_MEMORY_ACCESS_ANY_OWNER)) {
                    return TEE_ERROR_ACCESS_DENIED;
                }
                if ((flags & f) != flags)
                    return TEE_ERROR_ACCESS_DENIED;
            }
            break;
        default:
            break;
        }
    }

    return TEE_SUCCESS;
}

/**
 * 检查缓冲区是否重叠
 */
static bool bufs_intersect(void *buf1, size_t sz1, void *buf2, size_t sz2)
{
    vaddr_t b1 = (vaddr_t)buf1;
    vaddr_t b2 = (vaddr_t)buf2;
    vaddr_t e1 = b1 + sz1 - 1;
    vaddr_t e2 = b2 + sz2 - 1;

    if (!sz1 || !sz2) return false;
    if (e1 < b2 || e2 < b1) return false;
    return true;
}
```

## 4. Trusty分配器感知的堆检查

```c
/**
 * 检查缓冲区是否与堆内存重叠
 */
static bool trusty_buffer_overlaps_heap(void *buffer, size_t size)
{
    if (!buffer || !size) return false;

#ifdef RCTEE_TA_ALLOCATOR_SCUDO
    void *heap_base = scudo_get_heap_base();
    size_t heap_size = scudo_get_heap_size();
#else
    void *heap_base = dlmalloc_get_heap_base();
    size_t heap_size = dlmalloc_footprint();
#endif

    if (!heap_base || heap_size == 0) return false;

    uintptr_t heap_start = (uintptr_t)heap_base;
    uintptr_t heap_end = heap_start + heap_size;
    uintptr_t buf_start = (uintptr_t)buffer;
    uintptr_t buf_end = buf_start + size;

    return (buf_start < heap_end && buf_end > heap_start);
}

/**
 * 检查缓冲区是否在已分配的堆块内
 */
static bool trusty_buffer_is_within_allocated(void *buffer, size_t size)
{
    if (!buffer || !size) return false;

#ifdef RCTEE_TA_ALLOCATOR_SCUDO
    size_t usable_size = SCUDO_PREFIX(malloc_usable_size)(buffer);
#else
    size_t usable_size = dlmalloc_usable_size(buffer);
#endif

    if (usable_size == 0) return false;
    return (size <= usable_size);
}
```

## 5. 权限标志定义

```c
/* GP TEE标准权限标志 */
#define TEE_MEMORY_ACCESS_READ          0x00000001
#define TEE_MEMORY_ACCESS_WRITE         0x00000002
#define TEE_MEMORY_ACCESS_ANY_OWNER     0x00000004

/* Trusty MMU权限标志 */
#define ARCH_MMU_FLAG_PERM_USER         (1U<<2)  // 0x00000004
#define ARCH_MMU_FLAG_PERM_RO           (1U<<3)  // 0x00000008
#define ARCH_MMU_FLAG_PERM_NO_EXECUTE   (1U<<4)  // 0x00000010
#define ARCH_MMU_FLAG_NS                (1U<<5)  // 0x00000020
```

## 6. 系统调用表更新

```c
// kernel/rctee/lib/rctee/include/syscall_table.h
DEF_SYSCALL(0x42, _rctee_check_memory_access_rights, long, 3, void *buffer, uint32_t size, uint32_t access_flags)
```

## 7. actual_flags 格式与 TEE 标志比较

### 7.1 权限映射关系

| TEE 标志 | Trusty MMU 检查逻辑 | 说明 |
|---------|-------------------|------|
| `TEE_MEMORY_ACCESS_READ` | `actual_flags & ARCH_MMU_FLAG_PERM_USER` | 有USER权限即可读 |
| `TEE_MEMORY_ACCESS_WRITE` | `!(actual_flags & ARCH_MMU_FLAG_PERM_RO)` | 非只读即可写 |
| `TEE_MEMORY_ACCESS_ANY_OWNER` | `actual_flags & ARCH_MMU_FLAG_PERM_USER` | 需要USER权限 |

### 7.2 关键理解要点

1. **ARCH_MMU_FLAG_PERM_USER**：
   - 表示用户空间可以访问这个页面
   - 在 Trusty 中，有 USER 权限就意味着可读
   - 这是所有用户空间内存访问的基础要求

2. **ARCH_MMU_FLAG_PERM_RO**：
   - 表示页面是只读的
   - 如果设置了这个标志，就不能写入
   - 用于保护代码段、常量段等

3. **读权限检查**：
   - Trusty 中不需要单独的读权限标志
   - 有 `ARCH_MMU_FLAG_PERM_USER` 就可以读
   - 这与 x86/ARM 架构的设计一致

4. **写权限检查**：
   - 需要同时满足：有 USER 权限 + 没有 RO 标志
   - `ARCH_MMU_FLAG_PERM_RO` 是写权限的否定

## 8. 实现优势

### 8.1 与OP-TEE完全等价
- **三步检查架构**：与OP-TEE实际实现完全一致
- **MMU权限验证**：通过系统调用提供相同的权限查询能力
- **参数保护机制**：防止访问其他TA的参数内存
- **堆内存安全**：保护堆内存完整性

### 8.2 避免过度设计
- **简化策略**：不做OP-TEE实际上没有做的复杂检查
- **移除冗余**：去掉了内存类型识别、最小权限验证等过度复杂功能
- **性能优化**：减少不必要的检查开销
- **代码简洁**：逻辑清晰，易于维护

### 8.3 保持Trusty架构一致性
- **系统调用机制**：使用Trusty标准的系统调用接口
- **内核权限管理**：利用内核的完整MMU管理能力
- **用户空间简洁性**：用户空间逻辑清晰，复杂度在内核
- **分配器感知**：支持Trusty的scudo和dlmalloc分配器

## 9. 实现优先级

1. **第一优先级**：实现`_rctee_check_memory_access_rights`系统调用
2. **第二优先级**：实现Trusty分配器感知的堆检查函数
3. **第三优先级**：实现TA参数冲突检查和完整的用户空间接口

## 10. 总结

### 10.1 最终方案特点

1. **与OP-TEE完全一致**：遵循经过验证的实现模式
2. **符合GP标准**：满足实际的GP标准要求
3. **简单可靠**：代码简洁，逻辑清晰
4. **架构兼容**：保持Trusty系统的设计理念
5. **性能优化**：避免了不必要的复杂检查

### 10.2 关键技术决策

- **采用三步检查**：MMU权限 + 参数重叠 + 堆安全
- **简化权限映射**：USER权限包含读权限，RO标志控制写权限
- **系统调用实现**：在内核层面进行MMU查询和权限验证
- **分配器感知**：支持多种堆分配器的安全检查

**结论**：这个统一方案既符合GP标准，又与OP-TEE的成熟实现保持一致，是TEE_CheckMemoryAccessRights在Trusty平台上的最佳实现方案。
