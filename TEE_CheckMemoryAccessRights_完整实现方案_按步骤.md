# Trusty TEE_CheckMemoryAccessRights 完整实现方案（按实现步骤）

## 1. 方案概述

### 1.1 设计原则
基于对OP-TEE实际实现的深入分析，本方案采用**分层实现架构**：
1. **用户空间层**：GP标准接口和基础检查
2. **系统调用层**：内核级别的内存映射权限验证  
3. **内核实现层**：MMU查询、内存类型判断、权限验证

### 1.2 核心特点
- **与OP-TEE完全等价**：遵循OP-TEE的成熟实现模式
- **精确内存分类**：准确区分私有内存和共享内存
- **系统调用优化**：在内核层面进行高效的MMU权限查询
- **架构一致性**：保持Trusty系统的设计理念

## 2. 实现步骤

### 步骤1：用户空间GP接口实现

#### 2.1 文件位置
```
user/base/lib/libutee/tee_api.c
```

#### 2.2 GP标准接口实现
```c
#include <tee_internal_api.h>
#include <rctee_syscalls.h>

/**
 * GP标准TEE_CheckMemoryAccessRights实现
 * 完全遵循GP标准语义
 */
TEE_Result TEE_CheckMemoryAccessRights(uint32_t accessFlags, void *buffer, size_t size)
{
    /* GP标准：size为0时直接返回成功 */
    if (!size)
        return TEE_SUCCESS;
    
    /* 通过系统调用进行完整的权限检查 */
    long result = _rctee_check_memory_access_rights(accessFlags, buffer, size);
    
    /* 转换系统调用返回值为GP标准错误码 */
    switch (result) {
        case NO_ERROR:
            return TEE_SUCCESS;
        case ERR_INVALID_ARGS:
            return TEE_ERROR_BAD_PARAMETERS;
        case ERR_ACCESS_DENIED:
            return TEE_ERROR_ACCESS_DENIED;
        default:
            return TEE_ERROR_GENERIC;
    }
}
```

### 步骤2：系统调用接口定义

#### 2.3 系统调用号定义
```c
// user/base/include/rctee_syscalls.h
#define __NR_rctee_check_memory_access_rights  0x42

// 系统调用声明
long _rctee_check_memory_access_rights(uint32_t access_flags, void *buffer, size_t size);
```

#### 2.4 系统调用表注册
```c
// kernel/rctee/lib/rctee/rctee_core/syscall_table.c
DEF_SYSCALL(0x42, _rctee_check_memory_access_rights, long, 3, 
           uint32_t access_flags, user_addr_t buffer, uint32_t size)
```

### 步骤3：内核系统调用实现

#### 2.5 文件位置
```
kernel/rctee/lib/rctee/rctee_core/syscall.c
```

#### 2.6 主要实现函数
```c
#include <kernel/vm.h>
#include <arch/mmu.h>
#include <rctee_app.h>

/**
 * 内核系统调用实现
 * 参考OP-TEE的vm_check_access_rights实现
 */
long sys__rctee_check_memory_access_rights(uint32_t access_flags, 
                                          user_addr_t buffer, 
                                          uint32_t size) {
    struct rctee_app *rctee_app = current_rctee_app();
    vaddr_t vaddr = (vaddr_t)buffer;
    vaddr_t end_addr;
    
    /* 基本参数验证 */
    if (!buffer || !size) 
        return ERR_INVALID_ARGS;
    
    if (add_overflow(vaddr, size, &end_addr))
        return ERR_INVALID_ARGS;
    
    /* 验证访问标志的有效性 */
    uint32_t valid_flags = TEE_MEMORY_ACCESS_READ | 
                          TEE_MEMORY_ACCESS_WRITE | 
                          TEE_MEMORY_ACCESS_ANY_OWNER;
    if (access_flags & ~valid_flags)
        return ERR_INVALID_ARGS;
    
    /* 所有权检查 - 参考OP-TEE的vm_buf_is_inside_um_private */
    if (!(access_flags & TEE_MEMORY_ACCESS_ANY_OWNER)) {
        if (!is_ta_private_memory(rctee_app, vaddr, size))
            return ERR_ACCESS_DENIED;
    }
    
    /* 逐页MMU权限检查 - 参考OP-TEE的逐页检查 */
    while (vaddr < end_addr) {
        uint32_t mmu_flags;
        paddr_t paddr;
        
        status_t ret = arch_mmu_query(&rctee_app->aspace->arch_aspace, 
                                     vaddr, &paddr, &mmu_flags);
        if (ret != NO_ERROR)
            return ERR_ACCESS_DENIED;
        
        /* 检查基本用户权限 */
        if (!(mmu_flags & ARCH_MMU_FLAG_PERM_USER))
            return ERR_ACCESS_DENIED;
        
        /* 检查读权限 - 在Trusty中，有USER权限就可读 */
        if ((access_flags & TEE_MEMORY_ACCESS_READ)) {
            /* 已经在上面检查过USER权限了 */
        }
        
        /* 检查写权限 - 对应OP-TEE的TEE_MATTR_UW检查 */
        if ((access_flags & TEE_MEMORY_ACCESS_WRITE)) {
            if (mmu_flags & ARCH_MMU_FLAG_PERM_RO)
                return ERR_ACCESS_DENIED;
        }
        
        vaddr = ROUNDUP(vaddr + 1, PAGE_SIZE);
    }
    
    return NO_ERROR;
}
```

### 步骤4：内存类型判断实现

#### 2.7 私有内存检查函数
```c
/**
 * 检查内存是否为TA私有内存
 * 基于Trusty的内存管理机制进行精确判断
 */
static bool is_ta_private_memory(struct rctee_app *rctee_app, 
                                vaddr_t vaddr, size_t size) {
    vaddr_t end_addr = vaddr + size;
    
    /* 1. 检查是否为共享内存 (FF-A) */
    if (is_shared_memory_region(rctee_app, vaddr, size)) {
        return false;  // 共享内存不是私有的
    }
    
    /* 2. 检查是否在TA的地址空间范围内 */
    if (!is_in_ta_address_space(rctee_app, vaddr, size)) {
        return false;  // 超出TA地址空间
    }
    
    /* 3. 检查具体内存区域类型 */
    while (vaddr < end_addr) {
        enum trusty_memory_type mem_type = get_memory_type(rctee_app, vaddr);
        
        switch (mem_type) {
            case TRUSTY_MEM_PRIVATE:
            case TRUSTY_MEM_HEAP:
            case TRUSTY_MEM_STACK:
            case TRUSTY_MEM_CODE:
            case TRUSTY_MEM_DATA:
                /* 这些都是TA私有内存 */
                break;
                
            case TRUSTY_MEM_SHARED:
                /* 共享内存不是私有的 */
                return false;
                
            default:
                /* 未知类型，保守处理 */
                return false;
        }
        
        vaddr = ROUNDUP(vaddr + 1, PAGE_SIZE);
    }
    
    return true;  // 所有页面都是私有的
}
```

## 3. 实现优先级

### 3.1 第一阶段（核心功能）
1. **步骤1**：实现用户空间GP接口
2. **步骤2**：定义系统调用接口
3. **步骤3**：实现基础的内核系统调用（简化版内存检查）

### 3.2 第二阶段（完整功能）
4. **步骤4**：实现完整的内存类型判断
5. 添加共享内存检查机制
6. 完善错误处理和边界情况

### 3.3 第三阶段（优化和测试）
7. 性能优化
8. 完整的单元测试
9. 与OP-TEE兼容性验证

## 4. 关键技术决策

### 4.1 权限映射策略
- **读权限**：`ARCH_MMU_FLAG_PERM_USER` 包含读权限
- **写权限**：检查 `!(ARCH_MMU_FLAG_PERM_RO)`
- **所有权**：通过VMM区域和FF-A机制判断

### 4.2 内存分类机制
- **私有内存**：代码、数据、堆、栈
- **共享内存**：FF-A共享对象、VMM_FLAG_SHARED区域
- **精确判断**：基于VMM区域属性和对象类型

### 4.3 错误处理策略
- **保守原则**：有疑问时拒绝访问
- **标准兼容**：返回GP标准错误码
- **调试友好**：提供详细的错误信息

## 5. 详细实现细节

### 5.1 共享内存检查实现
```c
/**
 * 检查是否为共享内存区域
 */
static bool is_shared_memory_region(struct rctee_app *rctee_app,
                                   vaddr_t vaddr, size_t size) {
    struct vmm_aspace *aspace = rctee_app->aspace;
    vaddr_t end_addr = vaddr + size;

    /* 遍历地址空间中的所有区域 */
    struct vmm_region *region;
    list_for_every_entry(&aspace->region_list, region, struct vmm_region, node) {
        /* 检查是否与指定范围重叠 */
        if (vaddr < (region->base + region->size) && end_addr > region->base) {
            /* 检查区域标志，判断是否为共享内存 */
            if (region->flags & VMM_FLAG_SHARED) {
                return true;  // 发现共享内存区域
            }

            /* 检查是否为FF-A共享内存对象 */
            if (region->obj && is_ffa_shared_memory_obj(region->obj)) {
                return true;  // FF-A共享内存
            }
        }
    }

    return false;  // 没有发现共享内存
}

/**
 * 检查是否为FF-A共享内存对象
 */
static bool is_ffa_shared_memory_obj(struct vmm_obj *obj) {
    /* 检查对象类型是否为共享内存 */
    if (obj->ops && obj->ops->get_page) {
        /* 通过对象操作函数判断是否为共享内存 */
        return (obj->ops == &shared_mem_obj_ops);
    }

    return false;
}
```

### 5.2 内存类型识别实现
```c
/* Trusty内存类型枚举 */
enum trusty_memory_type {
    TRUSTY_MEM_PRIVATE,     // TA私有内存
    TRUSTY_MEM_SHARED,      // 共享内存
    TRUSTY_MEM_HEAP,        // 堆内存
    TRUSTY_MEM_STACK,       // 栈内存
    TRUSTY_MEM_CODE,        // 代码段
    TRUSTY_MEM_DATA,        // 数据段
};

/**
 * 获取内存类型
 */
static enum trusty_memory_type get_memory_type(struct rctee_app *rctee_app,
                                              vaddr_t vaddr) {
    struct vmm_aspace *aspace = rctee_app->aspace;
    struct vmm_region *region;

    /* 查找包含该地址的区域 */
    list_for_every_entry(&aspace->region_list, region, struct vmm_region, node) {
        if (vaddr >= region->base && vaddr < (region->base + region->size)) {
            /* 根据区域属性判断内存类型 */
            if (region->flags & VMM_FLAG_SHARED) {
                return TRUSTY_MEM_SHARED;
            }

            /* 根据权限和用途判断 */
            uint32_t flags = region->arch_mmu_flags;

            if (flags & ARCH_MMU_FLAG_PERM_EXECUTE) {
                return TRUSTY_MEM_CODE;  // 可执行 = 代码段
            } else if (flags & ARCH_MMU_FLAG_PERM_WRITE) {
                /* 可写内存，进一步判断是堆还是数据段 */
                if (is_heap_region(rctee_app, vaddr)) {
                    return TRUSTY_MEM_HEAP;
                } else if (is_stack_region(rctee_app, vaddr)) {
                    return TRUSTY_MEM_STACK;
                } else {
                    return TRUSTY_MEM_DATA;  // 数据段
                }
            } else {
                return TRUSTY_MEM_DATA;  // 只读数据
            }
        }
    }

    return TRUSTY_MEM_PRIVATE;  // 默认为私有内存
}

/**
 * 检查是否为堆区域
 */
static bool is_heap_region(struct rctee_app *rctee_app, vaddr_t vaddr) {
    /* 检查是否在堆地址范围内 */
    if (rctee_app->heap_base && rctee_app->heap_size) {
        return (vaddr >= rctee_app->heap_base &&
                vaddr < (rctee_app->heap_base + rctee_app->heap_size));
    }

    return false;
}

/**
 * 检查是否为栈区域
 */
static bool is_stack_region(struct rctee_app *rctee_app, vaddr_t vaddr) {
    /* 检查是否在栈地址范围内 */
    if (rctee_app->stack_base && rctee_app->stack_size) {
        return (vaddr >= rctee_app->stack_base &&
                vaddr < (rctee_app->stack_base + rctee_app->stack_size));
    }

    return false;
}
```

### 5.3 地址空间检查实现
```c
/**
 * 检查是否在TA地址空间范围内
 */
static bool is_in_ta_address_space(struct rctee_app *rctee_app,
                                  vaddr_t vaddr, size_t size) {
    struct vmm_aspace *aspace = rctee_app->aspace;
    vaddr_t end_addr = vaddr + size;

    /* 检查地址范围是否在用户空间 */
    if (vaddr < aspace->base || end_addr > (aspace->base + aspace->size)) {
        return false;  // 超出地址空间范围
    }

    return true;
}
```

## 6. 测试和验证

### 6.1 单元测试用例
```c
/* 测试用例1：基本权限检查 */
void test_basic_access_rights() {
    char buffer[1024];
    TEE_Result result;

    /* 测试读权限 */
    result = TEE_CheckMemoryAccessRights(TEE_MEMORY_ACCESS_READ,
                                        buffer, sizeof(buffer));
    assert(result == TEE_SUCCESS);

    /* 测试写权限 */
    result = TEE_CheckMemoryAccessRights(TEE_MEMORY_ACCESS_WRITE,
                                        buffer, sizeof(buffer));
    assert(result == TEE_SUCCESS);
}

/* 测试用例2：边界条件 */
void test_boundary_conditions() {
    TEE_Result result;

    /* 测试size=0 */
    result = TEE_CheckMemoryAccessRights(TEE_MEMORY_ACCESS_READ, NULL, 0);
    assert(result == TEE_SUCCESS);

    /* 测试NULL指针 */
    result = TEE_CheckMemoryAccessRights(TEE_MEMORY_ACCESS_READ, NULL, 1024);
    assert(result == TEE_ERROR_BAD_PARAMETERS);
}
```

### 6.2 性能基准测试
```c
/* 性能测试：大内存区域检查 */
void benchmark_large_memory_check() {
    char *large_buffer = malloc(1024 * 1024);  // 1MB
    uint64_t start_time, end_time;

    start_time = current_time_us();
    TEE_CheckMemoryAccessRights(TEE_MEMORY_ACCESS_READ | TEE_MEMORY_ACCESS_WRITE,
                               large_buffer, 1024 * 1024);
    end_time = current_time_us();

    printf("Large memory check took: %llu us\n", end_time - start_time);
    free(large_buffer);
}
```

## 7. 总结

这个按步骤组织的实现方案：
1. **结构清晰**：从用户空间到内核的完整实现路径
2. **可分阶段实施**：支持渐进式开发和测试
3. **技术可靠**：基于OP-TEE成熟实现的经验
4. **架构一致**：保持Trusty系统的设计理念
5. **功能完整**：包含内存类型判断、共享内存检查等完整功能
6. **可测试性**：提供完整的测试用例和性能基准
